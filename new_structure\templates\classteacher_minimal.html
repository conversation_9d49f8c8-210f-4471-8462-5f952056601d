<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Class Teacher Dashboard - Minimal</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />

    <!-- Custom Colors - Minimal -->
    <style>
      :root {
        --custom-bg: #fafafa;
        --custom-primary: #263ad1;
        --custom-secondary: #484b6a;
        --custom-accent: #f9cc48;
        --custom-text: #17cfe0;
      }

      body {
        background-color: var(--custom-bg) !important;
        color: var(--custom-primary) !important;
      }

      .navbar {
        background-color: var(--custom-bg) !important;
        border-bottom: 1px solid #e8e8e8 !important;
      }

      .card {
        background-color: white !important;
        border: 1px solid #e8e8e8 !important;
      }

      .btn-primary {
        background-color: var(--custom-accent) !important;
        border-color: var(--custom-accent) !important;
        color: black !important;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        color: var(--custom-primary) !important;
      }

      p {
        color: var(--custom-secondary) !important;
      }

      a {
        color: var(--custom-text) !important;
      }
    </style>
  </head>
  <body>
    <nav class="navbar navbar-expand-lg">
      <div class="container-fluid">
        <a class="navbar-brand" href="#">
          <i class="fas fa-graduation-cap"></i>
          Hillview School - Minimal
        </a>
      </div>
    </nav>

    <div class="container mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title mb-0">
                <i class="fas fa-user-tie"></i>
                Class Teacher Dashboard - Test Mode
              </h3>
            </div>
            <div class="card-body">
              <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <strong>Success!</strong> The minimal version is working with
                your custom colors.
              </div>

              <h4>Your Custom Colors:</h4>
              <ul>
                <li><strong>Background:</strong> #FAFAFA (Light Gray)</li>
                <li><strong>Primary Text:</strong> #263AD1 (Blue)</li>
                <li><strong>Secondary Text:</strong> #484B6A (Dark Gray)</li>
                <li><strong>Accent/Button:</strong> #F9CC48 (Yellow)</li>
                <li><strong>Link Text:</strong> #17cfe0 (Cyan)</li>
              </ul>

              <div class="mt-4">
                <button class="btn btn-primary me-2">
                  <i class="fas fa-palette"></i>
                  Primary Button (Yellow)
                </button>
                <a href="/classteacher/" class="btn btn-outline-primary">
                  <i class="fas fa-arrow-left"></i>
                  Back to Full Dashboard
                </a>
              </div>

              <div class="mt-4">
                <p>
                  This minimal version should load without crashes. If this
                  works but the full dashboard crashes, we know the issue is in
                  the complex template.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
